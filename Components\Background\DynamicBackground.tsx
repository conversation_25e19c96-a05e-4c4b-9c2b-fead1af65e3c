// Déclaration TypeScript pour window.sunriseControls
declare global {
  interface Window {
    sunriseControls?: {
      getIntensity?: () => number;
    };
  }
}
import React, { useEffect, useRef } from 'react';
// 🌅 CISCO: NETTOYAGE RADICAL - SEULS MODULES NÉCESSAIRES
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './DiurnalLayer';
import ModeLeverSoleil from './ModeLeverSoleil';

// 🌅 CISCO: NETTOYAGE RADICAL - SEUL MODE AUTORISÉ

interface DynamicBackgroundProps {
  skyMode?: string;
  children?: React.ReactNode;
}

const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ 
  skyMode = 'leverSoleil', 
  children 
}) => {
  // 🌅 CISCO: RÉFÉRENCES MINIMALES
  const landscapeRef = useRef<HTMLDivElement>(null);
  // zoomTimelineRef supprimé - animation zoom désactivée

  // 🌅 CISCO: FONCTION ZOOM/DÉZOOM PAYSAGE SUPPRIMÉE
  // Animation de zoom désactivée pour éliminer le "rectangle mobile suspect"

  // 🌅 CISCO: INITIALISATION MINIMALE
  // Synchronisation de la luminosité du paysage avec le lever du soleil
  useEffect(() => {
    let syncInterval: NodeJS.Timeout | null = null;
    const updateLandscapeBrightness = () => {
      // Récupère l'intensité du soleil via window.sunriseControls si disponible
      let intensity = 0.0;
      if (typeof window !== 'undefined' && window.sunriseControls && typeof window.sunriseControls.getIntensity === 'function') {
        intensity = window.sunriseControls.getIntensity();
      }
      // Synchronisation : l'éclairage du paysage ne démarre que lorsque le soleil commence à se lever
      // Référence : le soleil commence à se lever à intensity > 0.25 (voir ModeLeverSoleil)
      // Lever de soleil subtil : luminosité max = 0.55 (jamais zénith)
      const sunRiseThreshold = 0.25;
      let brightness = 0.05;
      // Retard de 10 secondes sur l'effet d'éclairage
      const delay = 0.15; // 15% de la progression (15s si timer 100s)
      const effectStart = sunRiseThreshold + delay; // Ex: 0.25 + 0.15 = 0.40
      if (intensity > effectStart) {
        // Progression encore plus allongée : effectStart → 1.0
        const progress = (intensity - effectStart) / (1.0 - effectStart);
        brightness = 0.05 + Math.max(0, Math.min(progress, 1)) * 0.13; // 0.05 → 0.18 (effet très subtil)
      }
      if (landscapeRef.current) {
        landscapeRef.current.style.filter = `brightness(${brightness})`;
      }
    };
    // Mise à jour toutes les 200ms pour fluidité
    syncInterval = setInterval(updateLandscapeBrightness, 200);
    return () => {
      if (syncInterval) clearInterval(syncInterval);
    };
  }, []);

  return (
    <div
      className="fixed inset-0 w-full h-full overflow-hidden"
      style={{
        backgroundColor: '#0B1426'
      }}
    >
      {/* 🌅 CISCO: COUCHE NUAGES (repère visuel, ne pas supprimer) */}
      <DiurnalLayer skyMode={skyMode} />

      {/* 🌅 CISCO: MODULE LEVER DE SOLEIL COMPLET (repère visuel, ne pas supprimer) */}
      <ModeLeverSoleil
        isActive={skyMode === 'leverSoleil'}
        autoStart={true}
        intensity={0.0}
      />

      {/* 🌅 CISCO: PAYSAGE FIXE (repère visuel, ne pas supprimer) */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: 'url(/Background.png)',
          backgroundPosition: 'center bottom -200px',
          backgroundSize: 'cover',
          zIndex: 10,
          transformOrigin: 'center center',
          willChange: 'transform, filter'
        }}
      />
      {/* Contenu principal scrollable */}
      <div
        className="relative main-scroll-content"
        style={{
          zIndex: 15,
          position: 'relative',
          height: '100vh',
          overflowY: 'auto',
          scrollbarWidth: 'none', // Firefox
          msOverflowStyle: 'none' // IE/Edge
        }}
      >
        {children}
      </div>
      {/* Masquage ascenseur pour Chrome/Safari/Edge */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .main-scroll-content::-webkit-scrollbar {
            width: 0px;
            background: transparent;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;
