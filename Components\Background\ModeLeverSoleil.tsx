import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
// 🌟 CISCO: ÉTOILES UNIFIÉES - SYSTÈME COMPLET
import UnifiedStars from './UnifiedStars';

interface ModeLeverSoleilProps {
  isActive: boolean;
  timerDuration?: number; // Durée du temporisateur en secondes (défaut: 120s)
  showDevModal?: boolean; // Affiche la modale d'infos dev
  onCloseDevModal?: () => void; // Ferme la modale d'infos dev
}

/**
 * 🌅 CISCO: SYSTÈME TIMELINE AVEC KEYFRAMES
 * 
 * NOUVEAU SYSTÈME RÉVOLUTIONNAIRE :
 * - Une seule timeline maître qui orchestre TOUT
 * - Keyframes temporels précis (8s, 10s, 15s, etc.)
 * - Synchronisation parfaite de tous les éléments
 * - Plus de calculs complexes de pourcentages
 * - Contrôle exact du timing
 */
const ModeLeverSoleil: React.FC<ModeLeverSoleilProps> = ({
  isActive,
  timerDuration = 120, // 120s VITESSE NORMALE
  showDevModal = false,
  onCloseDevModal
}) => {
  // 🎯 RÉFÉRENCES POUR LES ÉLÉMENTS
  const containerRef = useRef<HTMLDivElement>(null);
  const sunRef = useRef<HTMLDivElement>(null);
  const moonRef = useRef<HTMLImageElement>(null);
  const starsContainerRef = useRef<HTMLDivElement>(null);
  const globalLightRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const masterTimelineRef = useRef<gsap.core.Timeline | null>(null);
  
  // 🎵 Références audio multiples pour mixer les ambiances
  const nightAudioRefs = useRef<HTMLAudioElement[]>([]);
  const sunriseAudioRefs = useRef<HTMLAudioElement[]>([]);

  // 🌅 CISCO: DÉGRADÉ LEVER DE SOLEIL - Rose en bas, bleu en haut
  const SUNRISE_COLORS = {
    // Début : Nuit profonde
    NIGHT_TOP: '#001540',        // Bleu nuit profond (top)
    NIGHT_BOTTOM: '#001540',     // Bleu nuit profond (bottom)
    // Lever : Rose en bas, bleu clair en haut
    SUNRISE_TOP: '#4682B4',      // Bleu acier (top)
    SUNRISE_BOTTOM: '#FFC0CB',   // Rose (bottom)
    // Jour : Bleu ciel
    DAY_TOP: '#87CEEB',          // Bleu ciel clair (top)
    DAY_BOTTOM: '#E0F6FF'        // Bleu très clair (bottom)
  };

  // 🎵 GESTION AUDIO SELON CISCO INSTRUCTIONS
  const AUDIO_CONFIG = {
    night: {
      files: [
        '/sounds/nuit-profonde/night-atmosphere-with-crickets-374652.mp3',
        '/sounds/nuit-profonde/hibou-molkom.mp3'
      ],
      volumes: [0.6, 0.35]
    },
    sunrise: {
      files: [
        '/sounds/aube/village_morning_birds_roosters.mp3',
        '/sounds/lever-soleil/blackbird.mp3'
      ],
      volumes: [0.3, 0.4]
    },
    fadeOutDuration: 5000,  // 5 secondes de fade out
    fadeInDuration: 3000    // 3 secondes de fade in
  } as const;

  // 🎵 GESTION AUDIO PROGRESSIVE
  const playNightSound = () => {
    // Stop sunrise audios
    sunriseAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
    sunriseAudioRefs.current = [];

    // Stop existing night audios
    nightAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
    nightAudioRefs.current = [];

    AUDIO_CONFIG.night.files.forEach((file, idx) => {
      const a = new Audio(file);
      a.loop = true;
      a.volume = AUDIO_CONFIG.night.volumes[idx] ?? 0.5;
      a.play().catch(console.warn);
      nightAudioRefs.current.push(a);
    });
    console.log('🌙 Sons de nuit démarrés (criquets + hibou)');
  };

  const playSunriseSound = () => {
    // Fade out night sounds
    nightAudioRefs.current.forEach(audio => {
      gsap.to(audio, {
        volume: 0,
        duration: AUDIO_CONFIG.fadeOutDuration / 1000,
        ease: "power1.inOut",
        onComplete: () => audio.pause()
      });
    });

    // Sunrise sounds
    AUDIO_CONFIG.sunrise.files.forEach((file, idx) => {
      const audio = new Audio(file);
      audio.loop = true;
      audio.volume = 0;
      audio.play().catch(console.warn);
      
      gsap.to(audio, {
        volume: AUDIO_CONFIG.sunrise.volumes[idx] ?? 0.3,
        duration: AUDIO_CONFIG.fadeInDuration / 1000,
        ease: "power1.inOut"
      });
      
      sunriseAudioRefs.current.push(audio);
    });

    console.log('🌅 Sons de lever de soleil démarrés');
  };

  // 🎬 CISCO: TIMELINE MAÎTRE AVEC KEYFRAMES CORRIGÉS
  const createMasterTimeline = () => {
    console.log('🎬 CISCO: Création timeline maître avec keyframes RALENTIS');

    // Tuer l'ancienne timeline si elle existe
    if (masterTimelineRef.current) {
      masterTimelineRef.current.kill();
    }

    // Créer la nouvelle timeline maître
    masterTimelineRef.current = gsap.timeline({
      paused: true,
      onComplete: () => {
        console.log('🌅 CISCO: Timeline complète - Jour complet atteint');
      }
    });

    const tl = masterTimelineRef.current;
    const duration = timerDuration; // 120 secondes par défaut

    // 🌙 KEYFRAME 0s : DÉBUT - Nuit profonde
    tl.set([containerRef.current, sunRef.current, moonRef.current, globalLightRef.current], {
      clearProps: "all"
    }, 0);

    // Position initiale de la lune
    tl.set(moonRef.current, {
      y: "120%",
      left: "10%",
      opacity: 1,
      width: "140px",
      height: "140px"
    }, 0);

    // Position initiale du soleil (invisible)
    tl.set(sunRef.current, {
      y: "250%",
      opacity: 0,
      width: "80px",
      height: "80px"
    }, 0);

    // Dégradé initial (nuit)
    tl.set(containerRef.current, {
      backgroundImage: `linear-gradient(to bottom, ${SUNRISE_COLORS.NIGHT_TOP}, ${SUNRISE_COLORS.NIGHT_BOTTOM})`
    }, 0);

    // 🌙 KEYFRAME 0s → 90s : Mouvement de la lune ULTRA RALENTI
    tl.to(moonRef.current, {
      y: "1200%",
      left: "150%",
      duration: 90, // CISCO: 90 secondes - ENCORE plus lent
      ease: "none"
    }, 0);

    // 🎨 KEYFRAME 45s : Début changement dégradé (quand lune à mi-parcours)
    tl.to(containerRef.current, {
      backgroundImage: `linear-gradient(to bottom, ${SUNRISE_COLORS.SUNRISE_TOP}, ${SUNRISE_COLORS.SUNRISE_BOTTOM})`,
      duration: 30,
      ease: "power1.inOut"
    }, 45);

    console.log('🎨 CISCO: DÉGRADÉ DÉMARRE À 45 SECONDES');

    // ☀️ KEYFRAME 70s : Lune proche du paysage - DÉCLENCHEMENT SOLEIL
    tl.to(sunRef.current, {
      y: "25%",
      opacity: 1,
      duration: 30,
      ease: "power1.inOut"
    }, 70);

    // 💡 KEYFRAME 75s : Début éclairage global
    tl.to(globalLightRef.current, {
      opacity: 0.6, // CISCO: Moins fort
      duration: 25,
      ease: "power1.inOut"
    }, 75);

    // 🌄 KEYFRAME 75s : Éclairage du paysage CORRIGÉ
    tl.call(() => {
      // CISCO: Éclairage PERMANENT du paysage Background.png
      const landscapeElements = document.querySelectorAll('[style*="Background.png"]');
      landscapeElements.forEach(element => {
        // Démarrer sombre puis éclairer DÉFINITIVEMENT
        gsap.set(element, { filter: 'brightness(0.3)' }); // Départ sombre
        gsap.to(element, {
          filter: 'brightness(0.8)', // CISCO: Éclairage doux et PERMANENT
          duration: 25,
          ease: "power1.inOut"
        });
      });
      console.log('🌄 CISCO: Éclairage paysage PERMANENT démarré');
    }, [], 75);

    // ⭐ KEYFRAME 85s : Disparition des étoiles (quand lune presque disparue)
    tl.to(starsContainerRef.current, {
      opacity: 0,
      duration: 20,
      ease: "power2.out"
    }, 85);

    // 🎨 KEYFRAME 90s : Dégradé jour complet
    tl.to(containerRef.current, {
      backgroundImage: `linear-gradient(to bottom, ${SUNRISE_COLORS.DAY_TOP}, ${SUNRISE_COLORS.DAY_BOTTOM})`,
      duration: 20,
      ease: "power1.inOut"
    }, 90);

    // 🎵 KEYFRAMES AUDIO
    tl.call(playNightSound, [], 0);
    tl.call(playSunriseSound, [], 85); // Audio lever quand étoiles disparaissent

    console.log(`🎬 CISCO: Timeline ULTRA-RALENTIE créée - Durée totale: ${duration}s`);
    console.log(`🌙 CISCO: Lune descend sur 90s (TRÈS lent)`);
    console.log(`🎨 CISCO: DÉGRADÉ DÉMARRE À 45 SECONDES`);
    return tl;
  };

  // 🔄 EFFET PRINCIPAL - ACTIVATION/DÉSACTIVATION
  useEffect(() => {
    if (isActive) {
      console.log('🌅 CISCO: ACTIVATION MODULE LEVER DE SOLEIL - Timeline avec keyframes');
      
      const timeline = createMasterTimeline();
      timeline.play();
      
    } else {
      console.log('🌅 CISCO: DÉSACTIVATION MODULE LEVER DE SOLEIL');
      
      // Arrêter la timeline
      if (masterTimelineRef.current) {
        masterTimelineRef.current.kill();
      }
      
      // Arrêter tous les audios
      [...nightAudioRefs.current, ...sunriseAudioRefs.current].forEach(audio => {
        try { audio.pause(); } catch {}
      });
      nightAudioRefs.current = [];
      sunriseAudioRefs.current = [];
    }

    // Nettoyage au démontage
    return () => {
      if (masterTimelineRef.current) {
        masterTimelineRef.current.kill();
      }
      [...nightAudioRefs.current, ...sunriseAudioRefs.current].forEach(audio => {
        try { audio.pause(); } catch {}
      });
    };
  }, [isActive, timerDuration]);

  return (
    <>
      {/* 🌅 CISCO: CONTENEUR PRINCIPAL */}
      <div
        ref={containerRef}
        className="fixed inset-0 pointer-events-none"
        style={{
          zIndex: 5,
          opacity: isActive ? 1 : 0
        }}
      >
      {/* 🌙 LUNE AVEC HALO DISCRET */}
      <img
        ref={moonRef}
        src="/Lune-Moon.png"
        alt="Lune"
        className="absolute"
        style={{
          width: '140px',
          height: '140px',
          objectFit: 'contain',
          opacity: 1,
          zIndex: 2,
          pointerEvents: 'none',
          userSelect: 'none',
          filter: 'drop-shadow(0 0 35px rgba(255, 255, 255, 0.5)) drop-shadow(0 0 70px rgba(220, 220, 255, 0.3))',
          borderRadius: '50%'
        }}
      />

      {/* 🌞 SOLEIL RÉALISTE */}
      <div
        ref={sunRef}
        className="absolute"
        style={{
          left: '75%',
          top: '50%',
          transform: 'translate(-50%, -50%)',
          width: '80px',
          height: '80px',
          borderRadius: '50%',
          background: `radial-gradient(circle, #FFFFF0 0%, #FFFACD 25%, #FFD700 50%, #FFA500 75%, rgba(255,165,0,0.3) 100%)`,
          boxShadow: `0 0 100px 30px rgba(255,215,0,0.5), 0 0 140px 40px rgba(255,255,224,0.3)`,
          opacity: 0,
          zIndex: 3,
          pointerEvents: 'none',
          userSelect: 'none'
        }}
      />

      {/* ⭐ ÉTOILES UNIFIÉES */}
      <div ref={starsContainerRef}>
        <UnifiedStars
          skyMode="leverSoleil"
          isVisible={isActive}
          opacity={1}
        />
      </div>

      {/* 💡 ÉCLAIRAGE GLOBAL PROGRESSIF */}
      <div
        ref={globalLightRef}
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at 75% 60%,
            #FFE9B015 0%,
            #FFF8DC08 30%,
            #FFFDF505 50%,
            transparent 75%)`,
          opacity: 0,
          zIndex: 6
        }}
      />

      </div>
    </>
  );
};

export default ModeLeverSoleil;
