


# ModeLeverSoleil.tsx - Tout pour le lever du soleil
- Ajouter la gestion audio (nuit → lever de soleil)
Début =>> public\sounds\nuit-profonde\hibou-molkom.mp3
public\sounds\nuit-profonde\night-atmosphere-with-crickets-374652.mp3
public\sounds\nuit-profonde\sounds-crickets-nuit_profonde.mp3
Dès que le soleil se lève (les sons de la nuit disparaissent en fade out): 
ContextEngineering\Tasks\Cisco.md
public\sounds\lever-soleil\blackbird.mp3
public\sounds\lever-soleil\Lever_soleil-nature.mp3



0s    : Début - Nuit profonde, lune démarre
45s   : 🎨 DÉGRADÉ DÉMARRE (lune à mi-parcours)
70s   : ☀️ Soleil apparaît
75s   : 💡 Éclairage global + paysage PERMANENT
85s   : ⭐ Étoiles disparaissent + Audio lever
90s   : 🎨 Dégradé jour complet
120s  : Fin











































































